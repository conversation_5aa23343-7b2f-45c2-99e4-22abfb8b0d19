{"name": "fastq", "version": "1.15.0", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1756173717936, "integrity": "sha512-evchPJoEapp+9Kk5zZRA95xtvbXd0GFSaY4aac4rsnAZ0vneEh2tg31ybvorQDRRsid4MQBgM2kH5WpdchOY+w==", "mode": 420, "size": 765}, "bench.js": {"checkedAt": 1756173717937, "integrity": "sha512-+iDoaM4ZD8Zhfp3FWKoRnGS3uinwAuRJ00JjByAe7ncmIQnU0mRnp0Gtv8Sc5sDiGAX2MNSyjjxCf8ZFGhSMtw==", "mode": 420, "size": 1195}, "example.js": {"checkedAt": 1756173717938, "integrity": "sha512-mVyQ/Qr0vtPfvHYhmLmQE8jVm+w/kUGleDFk7AsB3+RZb+T+EqLa/6qt/b3naQVTQHtJmaJC+vv1pwDWkpAhPg==", "mode": 420, "size": 238}, "test/promise.js": {"checkedAt": 1756173717939, "integrity": "sha512-R690BEirZdB85FOqiSUK9TU5L5HVUfGuiWnWkSwFsu02Z8rkK897NFth12mLXxIr/JBRkoynNCG3JiDn6/Yw0g==", "mode": 420, "size": 5430}, "queue.js": {"checkedAt": 1756173717941, "integrity": "sha512-vNpy9CQBmhrST3fkAr1lOGdOjcm+22rnPNy3bzTYbcBzSIerzauY61ovJrmsEWF9WB41mIvM1BYVbLMDZq4z+g==", "mode": 420, "size": 5617}, "test/test.js": {"checkedAt": 1756173717948, "integrity": "sha512-MSrad5O4WQpnkD3Z7DLRw6KfiwXH6U6YSIEo+eHbtBLPNBg/SMnMC8koKYdMnQfjC4UC32Lgw1asKBFVYPhXcg==", "mode": 420, "size": 11624}, "package.json": {"checkedAt": 1756173717952, "integrity": "sha512-bzmKoA0z2i5HUT3Y3JfvfMB82aH7u1H/3+Rci9lDPXITNJfW25E17zJ/K5F7R14BA9IQ4iR/7pz4cxjtFBirRQ==", "mode": 420, "size": 1378}, "test/tsconfig.json": {"checkedAt": 1756173717953, "integrity": "sha512-IDz3YjBY1JHTNafNe7pvDTVHDTsJsJpTEF0auzU41Ie7/YvP7PbjpJL15NCx9kej0OpNNQWEVZGwFH7RUI182A==", "mode": 420, "size": 154}, "README.md": {"checkedAt": 1756173717954, "integrity": "sha512-Vd8OlDO9RcuAmF/XxrsHv1u/QDsfBKVTHntubPVOSTS0Y/fNrtIKveL0U17CiaBpnCMnW5P77Lm8IBLz6auABg==", "mode": 420, "size": 8319}, "example.mjs": {"checkedAt": 1756173717956, "integrity": "sha512-AFtzXI7xjdmMtpI/gIogVu2gHJbHj1NGQtvK4cFOMO7/KcWSMg9LNGLmwSpBlIP0g/V7cxG2giJjNdklEIerpQ==", "mode": 420, "size": 221}, "test/example.ts": {"checkedAt": 1756173717957, "integrity": "sha512-WLeRe9pA5IM7DDTrVYrVJp6CpHCiV8plHyqfqxAI7lImRXITNktiqCjbzkmO5Tt8x2mylybJZQMN6ZszDlg+Dg==", "mode": 420, "size": 1445}, "index.d.ts": {"checkedAt": 1756173717959, "integrity": "sha512-582DKPQwpq5NqTV0BZmMOoZLA5PHt1dkSsMmtm4lLTrMfRJVdshI0jmy+p5YjunlJ8dcAhSjGNf9+E3Y4o3C2g==", "mode": 420, "size": 1392}, ".github/workflows/ci.yml": {"checkedAt": 1756173717964, "integrity": "sha512-qBmSD+gO/d+vHKh/ZWZLJfvoUGhlkQPRowtIGCyxyIBydrG/v70HF2VYyJsJ7KcUOCmIFKgoDIlRWST9dA4AAA==", "mode": 420, "size": 915}, ".github/dependabot.yml": {"checkedAt": 1756173717965, "integrity": "sha512-X99brF4xMYFpV19YG2rwc9Z8c3Dd1RSctfiao7387XVAGS8hlzICHKT1w0uKLggjbN2D2gdG9Zk+blajxLe/QQ==", "mode": 420, "size": 192}}}