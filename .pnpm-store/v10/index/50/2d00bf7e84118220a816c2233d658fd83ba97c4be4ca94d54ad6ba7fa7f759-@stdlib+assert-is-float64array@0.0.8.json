{"name": "@stdlib/assert-is-float64array", "version": "0.0.8", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1756173718265, "integrity": "sha512-giZYqp2QbwsOHNySg9FuTpE8Yyro5HyjpXkP9Ck5aqbhCvG1sKLbnlYjh5mR2+1rIt/vv9FN8ffyrszTfwbkMg==", "mode": 420, "size": 23963}, "NOTICE": {"checkedAt": 1756173718266, "integrity": "sha512-4cuPRRYzgfVMbZIC790aV9oIoAEVC+4xyLwcB73N6IHP34DxsSH2gT61hzrQQsrrPKyZ3lwkqLKxnRVCIg2vCw==", "mode": 420, "size": 44}, "lib/index.js": {"checkedAt": 1756173718267, "integrity": "sha512-XseX6mNguzDzBQG3cD42GCe8ZlqkiR4a6bhMQHCvmcWrtJWjnwp3f6+B+UZUpxo3/xPLZHaMNXYpiLN7WfI9HA==", "mode": 420, "size": 1039}, "lib/main.js": {"checkedAt": 1756173718269, "integrity": "sha512-PKdtITcX3qhB8nJ0YhN+TCSNnDPkYACy9+8KnDBmEgg6ZfmaQzX0nTWbtndS5+AvcxGJ9cVCf7N0crxnmpNrUw==", "mode": 420, "size": 1411}, "package.json": {"checkedAt": 1756173718271, "integrity": "sha512-G+7jFqqjSs1Dzw77Zl50QHD4gdCjekyYwR2TMEu+Mo2mMDxVu4PyffzHFQjfwEI4vtt3PNT33qzumViFQ2ywew==", "mode": 420, "size": 2275}, "README.md": {"checkedAt": 1756173718273, "integrity": "sha512-h6zDu8e7hrUNZ1TIe7E2lLlIAIby1lyf87XiRuoiqJnbRqpWqBxobKOKoa646y6y2GSQcWqz3scWhpt8GXUAEg==", "mode": 420, "size": 5923}, "docs/types/index.d.ts": {"checkedAt": 1756173718275, "integrity": "sha512-EXYat8/Qvb7Q6c90uP78x6PweKTznxglvYlgHmzmFw/q/nLQ9EqW0MQAjfhY16MkMJF+gpwfIlpOQUaURwfuIg==", "mode": 420, "size": 1095}, "docs/types/test.ts": {"checkedAt": 1756173718277, "integrity": "sha512-p5DffQNwMPC4t2nA+aejpEXJUJLkvesFZgr78wlyviFXKXmLoBt63oUi1sWgln9rOZl+NWiM4Ogyu6xC8glLcg==", "mode": 420, "size": 1033}, "docs/repl.txt": {"checkedAt": 1756173718280, "integrity": "sha512-/GL86Gw/AB3YvHT3lBbhAMlJX74PqdrafQbzDf3LvwWSkY3uN93itCt6tzKKBX8Lwfp8PbLL+xf6XaNK0NAAgQ==", "mode": 420, "size": 407}}}