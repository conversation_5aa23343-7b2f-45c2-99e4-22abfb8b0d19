{"name": "@stdlib/assert-is-uint8array", "version": "0.0.8", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1756173718177, "integrity": "sha512-giZYqp2QbwsOHNySg9FuTpE8Yyro5HyjpXkP9Ck5aqbhCvG1sKLbnlYjh5mR2+1rIt/vv9FN8ffyrszTfwbkMg==", "mode": 420, "size": 23963}, "NOTICE": {"checkedAt": 1756173718177, "integrity": "sha512-4cuPRRYzgfVMbZIC790aV9oIoAEVC+4xyLwcB73N6IHP34DxsSH2gT61hzrQQsrrPKyZ3lwkqLKxnRVCIg2vCw==", "mode": 420, "size": 44}, "lib/index.js": {"checkedAt": 1756173718177, "integrity": "sha512-oykx0KDW4y+ndceIFU0FHjFgNPqCkFqrPRZV6lR+uN/PcrvDUcc8g07xX5Dwg/CO2KnPM+G9jF5F/O6Bmouf+w==", "mode": 420, "size": 1021}, "lib/main.js": {"checkedAt": 1756173718178, "integrity": "sha512-5lIxN6MM24dzWFyJiYh6lllD7hVQhy3ne/PPW0Z2xIwwfL/dFkVj0iuft3QTccniiVgA1v3/tB+rGKHbP7Blpw==", "mode": 420, "size": 1387}, "package.json": {"checkedAt": 1756173718178, "integrity": "sha512-x/qL1/PQFpC8eleO+ZdqXG+u5BryvkZl1PG+bjktuTPkGCz/hwyEClYI6c7KRVvykhPcn9Zl+WmagWL5J3xaXA==", "mode": 420, "size": 2329}, "README.md": {"checkedAt": 1756173718178, "integrity": "sha512-Fej90SUc9IAoynxeddp4j5c9xw8Mx79BQAuZcMrKngWkNK8N+d8wdrzcYfjQe8p8aaInYE4G19mlW6TI0gzcNQ==", "mode": 420, "size": 6426}, "docs/types/index.d.ts": {"checkedAt": 1756173718179, "integrity": "sha512-/MLl6REehbAJOS/kA+SYViEOF0zQboMC/c9gsSmEpX8YUyidWstr5qdyg38fMqVxdlo9akAT1oLo6Y5xPNMtZQ==", "mode": 420, "size": 1022}, "docs/types/test.ts": {"checkedAt": 1756173718179, "integrity": "sha512-3L21fGYA8YbZX2kWPIr+vedN+9tSf9AkJWUFrTxoldg8ax4/mmqCajll63p/eWEArzIl4gibnQK2rOjsE3Q7vw==", "mode": 420, "size": 1017}, "docs/repl.txt": {"checkedAt": 1756173718180, "integrity": "sha512-sWkobRCfORr2Sy8Rqh39QWBLFXhYJCYe4na7RInp+xKA6hi5yX7xUO0Oezwmh6T2CCQS8Ue5bnzYMs1tpLEwog==", "mode": 420, "size": 401}}}