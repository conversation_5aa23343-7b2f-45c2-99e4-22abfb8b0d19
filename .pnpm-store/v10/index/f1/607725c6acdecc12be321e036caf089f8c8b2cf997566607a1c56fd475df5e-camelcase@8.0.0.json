{"name": "camelcase", "version": "8.0.0", "requiresBuild": false, "files": {"license": {"checkedAt": 1756173718406, "integrity": "sha512-0fM2/ycrxrltyaBKfQ748Ck23VlPUUBgNAR47ldf4B1V/HoXTfWBSk+vcshGKwEpmOynu4mOP5o+hyBfuRNa8g==", "mode": 420, "size": 1117}, "index.js": {"checkedAt": 1756173718408, "integrity": "sha512-JRQdsRbqAFMx44XOVeQOxmfnEMnCpEYCAqwwc4V8NFmqkwqiD4gVNutyfHVdOo643i2fAzkKM3GQH22GxKtcCA==", "mode": 420, "size": 3568}, "package.json": {"checkedAt": 1756173718409, "integrity": "sha512-X+ixIyMUwM8OgWkDzh2juO6kk/4OQaWYDfL9RMo/ZGGEqBiixFyPM07JD6SPLPAVeXl6IIhSrUkhqh51OyIMig==", "mode": 420, "size": 883}, "readme.md": {"checkedAt": 1756173718411, "integrity": "sha512-+6poSzsKU0fbcC9tQ6W2cho/8ekHt+cf4ZyeclRPhNkIYRqXHiSRFiokUBsH0/Pg4ju7YBzGlr4bINafAVPf2Q==", "mode": 420, "size": 3143}, "index.d.ts": {"checkedAt": 1756173718413, "integrity": "sha512-4zIcr4UnEf/ylYZJRN3U5+pWKVVvvSP8qNo+o3mGj3LcjnJAxJjXW98f8BTxqtsxWsjH0qAaCl9I7BrHLTZ1zA==", "mode": 420, "size": 2452}}}